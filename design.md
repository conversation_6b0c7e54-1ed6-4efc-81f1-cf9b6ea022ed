# 英雄寵物冒險遊戲 - 系統設計文檔

## 1. 系統架構設計

### 1.1 整體架構
```
Client (StarterPlayerScripts)
├── UI Controllers (React + Reflex)
├── Game Controllers (Flamework)
└── Local Services

Server (ServerScriptService)  
├── Game Services (Flamework)
├── Data Management (ProfileService)
└── Combat & Economy Systems

Shared (ReplicatedStorage)
├── Types & Interfaces
├── Configuration Files
└── Utility Functions
```

### 1.2 核心技術棧
- **TypeScript**: 主要開發語言
- **Flamework**: 依賴注入和服務架構
- **React**: UI組件系統
- **Reflex**: 狀態管理
- **ProfileService**: 玩家數據持久化

## 2. 數據結構設計

### 2.1 玩家數據結構
```typescript
interface PlayerData {
    // 英雄屬性
    hero: {
        level: number;           // 1-100
        experience: number;      // 當前經驗值
        health: number;          // 當前血量
        maxHealth: number;       // 最大血量
        attack: number;          // 攻擊力
        defense: number;         // 防禦力
        critRate: number;        // 暴擊率
        critDamage: number;      // 暴擊傷害
    };
    
    // 貨幣系統
    currencies: {
        coins: number;           // 金幣
        gems: number;            // 寶石
        shopCoins: number;       // 商城幣
        experience: number;      // 經驗值貨幣
    };
    
    // 裝備系統
    equipment: {
        weapon?: Equipment;      // 武器
        armor?: Equipment;       // 防具
        accessory?: Equipment;   // 飾品
    };
    
    // 寵物系統
    pets: {
        active: Pet[];           // 當前攜帶寵物 (最多5隻)
        collection: PetRecord[]; // 寵物圖鑑記錄
    };
    
    // 進度數據
    progress: {
        unlockedAreas: string[]; // 已解鎖區域
        completedQuests: string[]; // 已完成任務
        achievements: string[];   // 已獲得成就
    };
    
    // 社交數據
    social: {
        friends: string[];       // 好友列表
        guildId?: string;        // 公會ID
        lastLogin: number;       // 上次登入時間
        loginStreak: number;     // 連續登入天數
    };
}
```

### 2.2 寵物數據結構
```typescript
interface Pet {
    id: string;                  // 唯一ID
    speciesId: string;          // 寵物種類ID
    name: string;               // 寵物名稱
    rarity: PetRarity;          // 稀有度
    level: number;              // 等級 (1-100)
    experience: number;         // 經驗值
    
    // 屬性
    stats: {
        health: number;         // 血量
        attack: number;         // 攻擊力
        defense: number;        // 防禦力
        speed: number;          // 速度
    };
    
    // 技能
    skills: PetSkill[];         // 寵物技能列表
    
    // 其他
    obtainedAt: number;         // 獲得時間
    isShiny: boolean;           // 是否為閃光
}

enum PetRarity {
    Common = "common",          // 普通
    Rare = "rare",             // 稀有  
    Epic = "epic",             // 史詩
    Mythic = "mythic",         // 神話
    Legendary = "legendary"     // 傳說
}
```

### 2.3 裝備數據結構
```typescript
interface Equipment {
    id: string;                 // 唯一ID
    itemId: string;            // 裝備種類ID
    name: string;              // 裝備名稱
    type: EquipmentType;       // 裝備類型
    rarity: ItemRarity;        // 品質等級
    level: number;             // 強化等級
    
    // 屬性加成
    stats: {
        attack?: number;        // 攻擊力加成
        defense?: number;       // 防禦力加成
        health?: number;        // 血量加成
        critRate?: number;      // 暴擊率加成
    };
    
    // 特殊效果
    effects: EquipmentEffect[]; // 特殊效果列表
}
```

## 3. 系統模組設計

### 3.1 英雄系統 (HeroService)
```typescript
@Service()
export class HeroService {
    // 等級計算
    calculateLevelFromExp(experience: number): number;
    calculateExpForLevel(level: number): number;
    
    // 屬性計算
    calculateMaxHealth(level: number): number;
    calculateBaseStats(level: number): HeroStats;
    
    // 升級處理
    levelUp(player: Player): void;
    gainExperience(player: Player, amount: number): void;
}
```

### 3.2 寵物系統 (PetService)
```typescript
@Service()
export class PetService {
    // 寵物管理
    addPetToPlayer(player: Player, pet: Pet): boolean;
    removePetFromPlayer(player: Player, petId: string): boolean;
    
    // 寵物升級
    gainPetExperience(player: Player, petId: string, amount: number): void;
    levelUpPet(player: Player, petId: string): void;
    
    // 扭蛋系統
    rollEgg(player: Player, eggType: EggType): Pet;
    calculateRarityChance(eggType: EggType): PetRarity;
}
```

### 3.3 戰鬥系統 (CombatService)
```typescript
@Service()
export class CombatService {
    // 戰鬥計算
    calculateDamage(attacker: CombatUnit, defender: CombatUnit): number;
    processCombatTurn(battle: Battle): CombatResult;
    
    // 戰鬥管理
    startBattle(player: Player, monster: Monster): Battle;
    endBattle(battle: Battle): BattleReward;
    
    // BOSS戰鬥
    createBossRaid(bossId: string): BossRaid;
    joinBossRaid(player: Player, raidId: string): boolean;
}
```

### 3.4 經濟系統 (EconomyService)
```typescript
@Service()
export class EconomyService {
    // 貨幣操作
    addCurrency(player: Player, type: CurrencyType, amount: number): void;
    removeCurrency(player: Player, type: CurrencyType, amount: number): boolean;
    
    // 交易系統
    createTrade(seller: Player, buyer: Player, items: TradeItem[]): Trade;
    completeTrade(tradeId: string): void;
    
    // 拍賣系統
    createAuction(seller: Player, item: Item, startPrice: number): Auction;
    placeBid(player: Player, auctionId: string, amount: number): boolean;
}
```

### 3.5 區域系統 (AreaService)
```typescript
@Service()
export class AreaService {
    // 區域管理
    unlockArea(player: Player, areaId: string): boolean;
    checkUnlockRequirements(player: Player, areaId: string): boolean;
    
    // 怪物生成
    spawnMonsters(areaId: string): Monster[];
    spawnBoss(areaId: string): Boss;
    
    // 資源管理
    getAreaResources(areaId: string): Resource[];
    collectResource(player: Player, resourceId: string): boolean;
}
```

## 4. UI系統設計

### 4.1 主界面布局
```
┌─────────────────────────────────────┐
│ 頂部狀態欄 (血量、等級、貨幣)        │
├─────────────────────────────────────┤
│                                     │
│         主遊戲區域                   │
│     (3D場景或戰鬥畫面)               │
│                                     │
├─────────────────────────────────────┤
│ 底部功能欄                          │
│ [背包][寵物][商店][任務][社交]       │
└─────────────────────────────────────┘
```

### 4.2 React組件架構
```typescript
// 主應用組件
export function GameApp() {
    return (
        <div className="game-container">
            <StatusBar />
            <GameView />
            <NavigationBar />
            <ModalManager />
        </div>
    );
}

// 狀態欄組件
export function StatusBar() {
    const playerData = useSelector(selectPlayerData);
    return (
        <div className="status-bar">
            <HealthBar health={playerData.hero.health} />
            <LevelDisplay level={playerData.hero.level} />
            <CurrencyDisplay currencies={playerData.currencies} />
        </div>
    );
}
```

### 4.3 狀態管理 (Reflex)
```typescript
// 玩家狀態切片
export interface PlayerState {
    data: PlayerData | null;
    isLoading: boolean;
    error: string | null;
}

export const playerSlice = createSlice({
    name: "player",
    initialState: initialPlayerState,
    reducers: {
        setPlayerData: (state, action) => ({
            ...state,
            data: action.payload,
            isLoading: false,
        }),
        updateHeroStats: (state, action) => ({
            ...state,
            data: state.data ? {
                ...state.data,
                hero: { ...state.data.hero, ...action.payload }
            } : null,
        }),
        addCurrency: (state, action) => {
            const { type, amount } = action.payload;
            if (state.data) {
                state.data.currencies[type] += amount;
            }
        },
    },
});
```

## 5. 網路通信設計

### 5.1 RemoteEvent 定義
```typescript
interface GameEvents {
    // 英雄系統
    "hero/gainExperience": (amount: number) => void;
    "hero/levelUp": () => void;
    
    // 寵物系統
    "pet/rollEgg": (eggType: EggType) => Pet;
    "pet/levelUpPet": (petId: string) => void;
    "pet/equipPet": (petId: string, slot: number) => void;
    
    // 戰鬥系統
    "combat/attackMonster": (monsterId: string) => CombatResult;
    "combat/joinBossRaid": (raidId: string) => boolean;
    
    // 經濟系統
    "economy/purchaseItem": (itemId: string, quantity: number) => boolean;
    "economy/createTrade": (targetPlayer: string, items: TradeItem[]) => string;
    
    // 社交系統
    "social/addFriend": (playerId: string) => boolean;
    "social/sendMessage": (targetId: string, message: string) => void;
}
```

### 5.2 數據同步策略
- **即時同步**: 戰鬥結果、貨幣變化、等級提升
- **定期同步**: 寵物狀態、裝備信息、進度數據
- **按需同步**: 好友列表、公會信息、排行榜

## 6. 配置系統設計

### 6.1 遊戲配置文件
```typescript
// 寵物配置
export const PET_CONFIG = {
    rarityChances: {
        [EggType.Basic]: {
            [PetRarity.Common]: 0.7,
            [PetRarity.Rare]: 0.25,
            [PetRarity.Epic]: 0.05,
        },
        [EggType.Premium]: {
            [PetRarity.Rare]: 0.4,
            [PetRarity.Epic]: 0.35,
            [PetRarity.Mythic]: 0.2,
            [PetRarity.Legendary]: 0.05,
        },
    },
    
    statMultipliers: {
        [PetRarity.Common]: 1.0,
        [PetRarity.Rare]: 1.5,
        [PetRarity.Epic]: 2.0,
        [PetRarity.Mythic]: 3.0,
        [PetRarity.Legendary]: 5.0,
    },
};

// 經濟配置
export const ECONOMY_CONFIG = {
    eggPrices: {
        [EggType.Basic]: { coins: 1000 },
        [EggType.Premium]: { gems: 100 },
        [EggType.Limited]: { shopCoins: 50 },
    },
    
    areaUnlockCosts: {
        "forest": { coins: 5000, level: 10 },
        "desert": { coins: 15000, level: 25 },
        "mountain": { coins: 35000, level: 50 },
    },
};
```

## 7. 安全性設計

### 7.1 防作弊機制
- **服務器驗證**: 所有重要操作在服務器端驗證
- **數據校驗**: 定期檢查玩家數據的合理性
- **行為監控**: 檢測異常的遊戲行為模式
- **限制機制**: 操作頻率和數量限制

### 7.2 數據保護
- **加密傳輸**: 敏感數據加密傳輸
- **備份機制**: 定期自動備份玩家數據
- **版本控制**: 數據結構版本管理和遷移
- **錯誤恢復**: 數據損壞的檢測和修復

## 8. 性能優化設計

### 8.1 客戶端優化
- **資源管理**: 按需載入和釋放資源
- **UI優化**: 虛擬化長列表，減少DOM操作
- **動畫優化**: 使用高效的動畫庫和技術

### 8.2 服務器優化
- **數據庫優化**: 合理的數據結構和查詢優化
- **緩存策略**: 熱點數據緩存機制
- **負載均衡**: 分散服務器負載

### 8.3 網路優化
- **數據壓縮**: 減少網路傳輸數據量
- **批量操作**: 合併多個小操作為批量操作
- **連接管理**: 優化網路連接的建立和維護

## 9. 遊戲平衡設計

### 9.1 經濟平衡公式
```typescript
// 英雄等級經驗值需求
const getExpForLevel = (level: number): number => {
    return Math.floor(100 * Math.pow(1.15, level - 1));
};

// 寵物屬性計算
const calculatePetStats = (level: number, rarity: PetRarity): PetStats => {
    const baseStats = {
        health: 50 + (level * 25),
        attack: 10 + (level * 5),
        defense: 5 + (level * 2),
        speed: 20 + (level * 1)
    };

    const multiplier = RARITY_MULTIPLIERS[rarity];
    return {
        health: Math.floor(baseStats.health * multiplier),
        attack: Math.floor(baseStats.attack * multiplier),
        defense: Math.floor(baseStats.defense * multiplier),
        speed: Math.floor(baseStats.speed * multiplier)
    };
};

// 戰鬥傷害計算
const calculateDamage = (attacker: CombatUnit, defender: CombatUnit): number => {
    const baseDamage = attacker.attack;
    const defense = defender.defense;
    const critChance = attacker.critRate || 0.05;
    const critMultiplier = attacker.critDamage || 1.5;

    let damage = Math.max(1, baseDamage - defense * 0.5);

    // 暴擊計算
    if (Math.random() < critChance) {
        damage *= critMultiplier;
    }

    // 隨機浮動 ±10%
    const variance = 0.9 + Math.random() * 0.2;
    return Math.floor(damage * variance);
};
```

### 9.2 扭蛋機率設計
```typescript
const GACHA_RATES = {
    starter: {
        [PetRarity.Common]: 1.0,     // 100% 普通
    },
    basic: {
        [PetRarity.Common]: 0.70,    // 70% 普通
        [PetRarity.Rare]: 0.25,      // 25% 稀有
        [PetRarity.Epic]: 0.05,      // 5% 史詩
    },
    premium: {
        [PetRarity.Rare]: 0.40,      // 40% 稀有
        [PetRarity.Epic]: 0.35,      // 35% 史詩
        [PetRarity.Mythic]: 0.20,    // 20% 神話
        [PetRarity.Legendary]: 0.05, // 5% 傳說
    },
    limited: {
        [PetRarity.Epic]: 0.30,      // 30% 史詩
        [PetRarity.Mythic]: 0.40,    // 40% 神話
        [PetRarity.Legendary]: 0.30, // 30% 傳說
    }
};

// 保底機制
const PITY_SYSTEM = {
    epic: { threshold: 20, guarantee: PetRarity.Epic },
    mythic: { threshold: 50, guarantee: PetRarity.Mythic },
    legendary: { threshold: 100, guarantee: PetRarity.Legendary }
};
```

### 9.3 區域解鎖成本設計
```typescript
const AREA_UNLOCK_COSTS = {
    "starter_town": { level: 1, coins: 0 },
    "green_forest": { level: 5, coins: 2000 },
    "dark_cave": { level: 15, coins: 8000 },
    "burning_desert": { level: 25, coins: 20000 },
    "frozen_peaks": { level: 40, coins: 50000 },
    "shadow_realm": { level: 60, coins: 100000 },
    "celestial_tower": { level: 80, coins: 200000 },
    "void_dimension": { level: 95, coins: 500000 }
};
```

## 10. 數據持久化設計

### 10.1 ProfileService 整合
```typescript
const ProfileTemplate = {
    // 版本控制
    dataVersion: 1,

    // 英雄數據
    hero: {
        level: 1,
        experience: 0,
        health: 150,
        maxHealth: 150,
        stats: {
            attack: 15,
            defense: 8,
            critRate: 0.05,
            critDamage: 1.5
        }
    },

    // 貨幣數據
    currencies: {
        coins: 1000,
        gems: 50,
        shopCoins: 10,
        experience: 0
    },

    // 寵物數據
    pets: {
        active: [],
        collection: [],
        nextId: 1
    },

    // 裝備數據
    equipment: {
        weapon: null,
        armor: null,
        accessory: null,
        inventory: []
    },

    // 進度數據
    progress: {
        unlockedAreas: ["starter_town"],
        completedQuests: [],
        achievements: [],
        gachaHistory: [],
        combatStats: {
            monstersDefeated: 0,
            bossesDefeated: 0,
            totalDamageDealt: 0
        }
    },

    // 社交數據
    social: {
        friends: [],
        guildId: null,
        lastLogin: 0,
        loginStreak: 0,
        dailyRewardsClaimed: []
    },

    // 設置數據
    settings: {
        soundEnabled: true,
        musicEnabled: true,
        notificationsEnabled: true,
        language: "zh-TW"
    }
};
```

### 10.2 數據遷移策略
```typescript
const DataMigration = {
    migrateToVersion2: (data: any) => {
        // 添加新的貨幣類型
        if (!data.currencies.shopCoins) {
            data.currencies.shopCoins = 0;
        }

        // 添加新的統計數據
        if (!data.progress.combatStats) {
            data.progress.combatStats = {
                monstersDefeated: 0,
                bossesDefeated: 0,
                totalDamageDealt: 0
            };
        }

        data.dataVersion = 2;
        return data;
    },

    migrateToVersion3: (data: any) => {
        // 重構寵物數據結構
        if (data.pets && !data.pets.collection) {
            data.pets.collection = data.pets.owned || [];
            data.pets.active = data.pets.equipped || [];
            delete data.pets.owned;
            delete data.pets.equipped;
        }

        data.dataVersion = 3;
        return data;
    }
};
```

## 11. 錯誤處理和日誌系統

### 11.1 錯誤分類和處理
```typescript
enum ErrorType {
    NetworkError = "NETWORK_ERROR",
    DataError = "DATA_ERROR",
    ValidationError = "VALIDATION_ERROR",
    GameLogicError = "GAME_LOGIC_ERROR",
    UIError = "UI_ERROR"
}

interface GameError {
    type: ErrorType;
    message: string;
    context?: any;
    timestamp: number;
    playerId?: string;
}

class ErrorHandler {
    static handleError(error: GameError): void {
        // 記錄錯誤
        this.logError(error);

        // 根據錯誤類型決定處理方式
        switch (error.type) {
            case ErrorType.NetworkError:
                this.handleNetworkError(error);
                break;
            case ErrorType.DataError:
                this.handleDataError(error);
                break;
            case ErrorType.ValidationError:
                this.handleValidationError(error);
                break;
            default:
                this.handleGenericError(error);
        }
    }

    private static handleNetworkError(error: GameError): void {
        // 顯示重試提示
        // 自動重試機制
        // 離線模式切換
    }

    private static handleDataError(error: GameError): void {
        // 數據恢復嘗試
        // 備份數據載入
        // 用戶通知
    }
}
```

### 11.2 性能監控
```typescript
class PerformanceMonitor {
    private static metrics: Map<string, number[]> = new Map();

    static startTimer(operation: string): () => void {
        const startTime = tick();

        return () => {
            const endTime = tick();
            const duration = endTime - startTime;

            if (!this.metrics.has(operation)) {
                this.metrics.set(operation, []);
            }

            this.metrics.get(operation)!.push(duration);

            // 如果操作時間過長，記錄警告
            if (duration > 0.1) { // 100ms
                warn(`Slow operation detected: ${operation} took ${duration}s`);
            }
        };
    }

    static getAverageTime(operation: string): number {
        const times = this.metrics.get(operation);
        if (!times || times.length === 0) return 0;

        return times.reduce((a, b) => a + b, 0) / times.length;
    }
}
```
